//
//  Exercise+CoreDataProperties.swift
//  DrMuscleWatchApp
//
//  Created by Dr<PERSON> <PERSON><PERSON><PERSON> on 2025-06-21.
//
//

import Foundation
import CoreData

extension Exercise {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<Exercise> {
        return NSFetchRequest<Exercise>(entityName: "Exercise")
    }

    @NSManaged public var id: Int64
    @NSManaged public var isBodyweight: Bool
    @NSManaged public var name: String
    @NSManaged public var workoutID: Int64
    @NSManaged public var setLogs: NSSet?
    @NSManaged public var workout: Workout?

}

// MARK: Generated accessors for setLogs
extension Exercise {

    @objc(addSetLogsObject:)
    @NSManaged public func addToSetLogs(_ value: SetLog)

    @objc(removeSetLogsObject:)
    @NSManaged public func removeFromSetLogs(_ value: SetLog)

    @objc(addSetLogs:)
    @NSManaged public func addToSetLogs(_ values: NSSet)

    @objc(removeSetLogs:)
    @NSManaged public func removeFromSetLogs(_ values: NSSet)

}

extension Exercise : Identifiable {

}
