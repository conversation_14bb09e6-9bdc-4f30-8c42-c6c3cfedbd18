//
//  SetLog+CoreDataProperties.swift
//  DrMuscleWatchApp
//
//  Created by Dr<PERSON> <PERSON><PERSON><PERSON> on 2025-06-21.
//
//

import Foundation
import CoreData

extension SetLog {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<SetLog> {
        return NSFetchRequest<SetLog>(entityName: "SetLog")
    }

    @NSManaged public var exerciseID: Int64
    @NSManaged public var isWarmup: Bool
    @NSManaged public var needsSync: Bool
    @NSManaged public var reps: Int16
    @NSManaged public var rir: Int16
    @NSManaged public var timestamp: Date
    @NSManaged public var weight: Double
    @NSManaged public var weightUnit: String
    @NSManaged public var workoutID: Int64
    @NSManaged public var exercise: Exercise?
    @NSManaged public var workout: Workout?

}

extension SetLog : Identifiable {

}
