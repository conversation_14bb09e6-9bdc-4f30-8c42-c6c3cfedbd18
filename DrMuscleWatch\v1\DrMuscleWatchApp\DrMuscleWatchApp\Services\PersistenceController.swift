import CoreData
import Combine

/// Manages the Core Data stack for the application
class PersistenceController: ObservableObject {
    /// Shared instance for the entire application
    static let shared = PersistenceController()
    
    /// Preview instance for SwiftUI previews and testing
    static var preview: PersistenceController = {
        let controller = PersistenceController(inMemory: true)
        
        // Create example data for previews if needed
        let viewContext = controller.container.viewContext
        
        // Example: Create a sample workout
        let workout = Workout(context: viewContext)
        workout.id = 1
        workout.name = "Sample Workout"
        workout.timestamp = Date()
        workout.isCompleted = false
        workout.needsSync = true
        
        // Example: Create a sample exercise
        let exercise = Exercise(context: viewContext)
        exercise.id = 1
        exercise.name = "Bench Press"
        exercise.workoutID = 1
        exercise.workout = workout
        
        // Example: Create a sample set log
        let setLog = SetLog(context: viewContext)
        setLog.exerciseID = 1
        setLog.workoutID = 1
        setLog.reps = 8
        setLog.weight = 100.0
        setLog.weightUnit = "kg"
        setLog.timestamp = Date()
        setLog.isWarmup = false
        setLog.needsSync = true
        setLog.exercise = exercise
        setLog.workout = workout
        
        do {
            try viewContext.save()
        } catch {
            let nsError = error as NSError
            fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
        }
        
        return controller
    }()
    
    /// The Core Data persistent container
    let container: NSPersistentContainer
    
    /// Initializes the Core Data stack
    /// - Parameter inMemory: If true, uses an in-memory store instead of a persistent store
    init(inMemory: Bool = false) {
        container = NSPersistentContainer(name: "DrMuscleData")
        
        if inMemory {
            container.persistentStoreDescriptions.first?.url = URL(fileURLWithPath: "/dev/null")
        }
        
        container.loadPersistentStores { description, error in
            if let error = error as NSError? {
                // Replace this implementation with code to handle the error appropriately.
                // fatalError() causes the application to generate a crash log and terminate.
                // You should not use this function in a shipping application, although it may be useful during development.
                
                /*
                 Typical reasons for an error here include:
                 * The parent directory does not exist, cannot be created, or disallows writing.
                 * The persistent store is not accessible, due to permissions or data protection when the device is locked.
                 * The device is out of space.
                 * The store could not be migrated to the current model version.
                 Check the error message to determine what the actual problem was.
                 */
                fatalError("Unresolved error \(error), \(error.localizedDescription)")
            }
        }
        
        // Enable automatic merging of changes from parent contexts
        container.viewContext.automaticallyMergesChangesFromParent = true
        
        // Configure merge policy to handle conflicts
        container.viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
    }
    
    /// Saves changes to the Core Data context if there are changes
    /// - Parameter context: The managed object context to save
    /// - Throws: An error if the save operation fails
    func save(_ context: NSManagedObjectContext) throws {
        if context.hasChanges {
            try context.save()
        }
    }
    
    /// Creates a new background context for performing operations off the main thread
    /// - Returns: A new managed object context with a private queue concurrency type
    func newBackgroundContext() -> NSManagedObjectContext {
        return container.newBackgroundContext()
    }
    
    /// Performs a batch delete operation for the specified fetch request
    /// - Parameters:
    ///   - fetchRequest: The fetch request specifying the objects to delete
    ///   - context: The managed object context to perform the deletion in
    /// - Returns: The number of objects deleted
    /// - Throws: An error if the batch delete operation fails
    func batchDelete<T: NSManagedObject>(_ fetchRequest: NSFetchRequest<T>, in context: NSManagedObjectContext) throws -> Int {
        let batchDeleteRequest = NSBatchDeleteRequest(fetchRequest: fetchRequest as! NSFetchRequest<NSFetchRequestResult>)
        batchDeleteRequest.resultType = .resultTypeObjectIDs
        
        let result = try context.execute(batchDeleteRequest) as? NSBatchDeleteResult
        let objectIDs = result?.result as? [NSManagedObjectID] ?? []
        
        let changes = [NSDeletedObjectsKey: objectIDs]
        NSManagedObjectContext.mergeChanges(fromRemoteContextSave: changes, into: [context])
        
        return objectIDs.count
    }
}
