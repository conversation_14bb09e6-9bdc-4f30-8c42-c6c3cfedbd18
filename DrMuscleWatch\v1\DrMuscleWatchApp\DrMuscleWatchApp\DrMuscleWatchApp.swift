//
//  DrMuscleWatchApp.swift
//  DrMuscleWatchApp
//
//  Created on May 1, 2024
//  Updated for TestFlight deployment
//

import SwiftUI

@main
struct DrMuscleWatchApp: App {
    // Initialize the Core Data stack
    @StateObject private var persistenceController = PersistenceController.shared

    // Initialize the authentication manager
    @StateObject private var authManager = AuthenticationManager.shared

    // Initialize the sync service
    private let syncService = SyncService.shared

    init() {
        // Start monitoring network connectivity when the app launches
        syncService.startMonitoring()
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .environmentObject(authManager)
                .onAppear {
                    // Attempt to sync any pending data when the app appears
                    Task {
                        await syncService.attemptSync()
                    }
                }
        }
    }
}
