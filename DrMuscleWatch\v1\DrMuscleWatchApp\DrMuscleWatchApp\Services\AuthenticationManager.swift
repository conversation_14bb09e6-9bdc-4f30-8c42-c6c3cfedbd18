import Foundation
import Security

/// Manages authentication state and operations for watchOS
class AuthenticationManager: NSObject, ObservableObject {
    /// Shared instance for the entire application
    static let shared = AuthenticationManager()



    /// Published property indicating whether the user is authenticated
    @Published var isAuthenticated: Bool = false

    /// The current authenticated user
    @Published var currentUser: UserInfosModel?

    /// Error message if authentication fails
    @Published var authError: String?

    /// Private initializer to enforce singleton pattern
    private override init() {
        super.init()
        loadAuthState()
    }

    /// Loads the authentication state from secure storage
    private func loadAuthState() {
        // Check if we have a stored token
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: "DrMuscleAuthToken",
            kSecReturnData as String: true
        ]

        var item: CFTypeRef?
        let status = SecItemCopyMatching(query as CFDictionary, &item)

        guard status == errSecSuccess,
              let tokenData = item as? Data,
              let token = String(data: tokenData, encoding: .utf8) else {
            isAuthenticated = false
            currentUser = nil
            return
        }



        // Load user info from UserDefaults
        if let userData = UserDefaults.standard.data(forKey: "DrMuscleUserInfo"),
           let userInfo = try? JSONDecoder().decode(UserInfosModel.self, from: userData) {
            currentUser = userInfo
            isAuthenticated = true
        } else {
            isAuthenticated = false
            currentUser = nil
        }
    }

    /// Stores the authentication state in secure storage
    func storeAuthState(userInfo: UserInfosModel) {
        // Store the token in the keychain
        let tokenData = userInfo.token.data(using: .utf8)!

        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: "DrMuscleAuthToken",
            kSecValueData as String: tokenData
        ]

        // First try to update the existing item
        var status = SecItemUpdate(query as CFDictionary, [kSecValueData as String: tokenData] as CFDictionary)

        // If the item doesn't exist, add it
        if status == errSecItemNotFound {
            status = SecItemAdd(query as CFDictionary, nil)
        }



        // Store user info in UserDefaults
        if let userData = try? JSONEncoder().encode(userInfo) {
            UserDefaults.standard.set(userData, forKey: "DrMuscleUserInfo")
        }

        // Update the published properties
        currentUser = userInfo
        isAuthenticated = true
        authError = nil
    }

    /// Clears the authentication state
    func clearAuthState() {
        // Remove the token from the keychain
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: "DrMuscleAuthToken"
        ]

        SecItemDelete(query as CFDictionary)



        // Remove user info from UserDefaults
        UserDefaults.standard.removeObject(forKey: "DrMuscleUserInfo")

        // Update the published properties
        currentUser = nil
        isAuthenticated = false
    }

    /// Authenticates with email and password (for watchOS)
    func signIn(email: String, password: String) async {
        do {
            // TODO: Implement API call to authenticate with email/password
            // For now, create a mock user for testing
            let mockUser = UserInfosModel(
                id: "mock-user-id",
                email: email,
                token: "mock-auth-token",
                firstName: "Watch",
                lastName: "User"
            )

            await MainActor.run {
                self.storeAuthState(userInfo: mockUser)
                self.authError = nil
            }
        } catch {
            await MainActor.run {
                self.authError = error.localizedDescription
            }
        }
    }
}
