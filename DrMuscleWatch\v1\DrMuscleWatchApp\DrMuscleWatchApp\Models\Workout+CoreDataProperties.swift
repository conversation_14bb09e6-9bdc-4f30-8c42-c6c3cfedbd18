//
//  Workout+CoreDataProperties.swift
//  DrMuscleWatchApp
//
//  Created by <PERSON><PERSON><PERSON> on 2025-06-21.
//
//

import Foundation
import CoreData

extension Workout {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<Workout> {
        return NSFetchRequest<Workout>(entityName: "Workout")
    }

    @NSManaged public var id: Int64
    @NSManaged public var isCompleted: Bool
    @NSManaged public var name: String
    @NSManaged public var needsSync: Bool
    @NSManaged public var timestamp: Date
    @NSManaged public var exercises: NSSet?
    @NSManaged public var setLogs: NSSet?

}

// MARK: Generated accessors for exercises
extension Workout {

    @objc(addExercisesObject:)
    @NSManaged public func addToExercises(_ value: Exercise)

    @objc(removeExercisesObject:)
    @NSManaged public func removeFromExercises(_ value: Exercise)

    @objc(addExercises:)
    @NSManaged public func addToExercises(_ values: NSSet)

    @objc(removeExercises:)
    @NSManaged public func removeFromExercises(_ values: NSSet)

}

// MARK: Generated accessors for setLogs
extension Workout {

    @objc(addSetLogsObject:)
    @NSManaged public func addToSetLogs(_ value: SetLog)

    @objc(removeSetLogsObject:)
    @NSManaged public func removeFromSetLogs(_ value: SetLog)

    @objc(addSetLogs:)
    @NSManaged public func addToSetLogs(_ values: NSSet)

    @objc(removeSetLogs:)
    @NSManaged public func removeFromSetLogs(_ values: NSSet)

}

extension Workout : Identifiable {

}
