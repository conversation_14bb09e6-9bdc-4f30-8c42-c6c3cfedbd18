//
//  SyncService.swift
//  DrMuscleWatchApp
//
//  Created by <PERSON><PERSON> on 2025-06-21.
//

import Foundation
import Network
import CoreData

/// Service responsible for syncing data with the remote API
class SyncService: ObservableObject {
    /// Shared instance for the entire application
    static let shared = SyncService()
    
    /// Network path monitor for connectivity checking
    private let monitor = NWPathMonitor()
    
    /// Queue for network monitoring
    private let queue = DispatchQueue(label: "NetworkMonitor")
    
    /// Current network connectivity status
    @Published var isConnected = false
    
    /// Indicates if a sync operation is currently in progress
    @Published var isSyncing = false
    
    /// Authentication manager for API requests
    private let authManager = AuthenticationManager.shared
    
    /// Storage service for data operations
    private let storageService = StorageService.shared
    
    private init() {
        setupNetworkMonitoring()
    }
    
    /// Sets up network connectivity monitoring
    private func setupNetworkMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isConnected = path.status == .satisfied
            }
        }
    }
    
    /// Starts monitoring network connectivity
    func startMonitoring() {
        monitor.start(queue: queue)
    }
    
    /// Stops monitoring network connectivity
    func stopMonitoring() {
        monitor.cancel()
    }
    
    /// Attempts to sync pending data with the remote API
    func attemptSync() async {
        guard isConnected && !isSyncing else { return }
        
        await MainActor.run {
            isSyncing = true
        }
        
        defer {
            Task { @MainActor in
                isSyncing = false
            }
        }
        
        do {
            // Sync pending set logs
            await syncPendingSetLogs()
            
            // Sync pending workouts
            await syncPendingWorkouts()
            
            print("Sync completed successfully")
        } catch {
            print("Sync failed: \(error.localizedDescription)")
        }
    }
    
    /// Syncs pending set logs to the remote API
    private func syncPendingSetLogs() async {
        do {
            let pendingSetLogs = try storageService.fetchPendingSetLogs()
            
            for setLog in pendingSetLogs {
                // TODO: Implement API call to sync set log
                // For now, just mark as synced
                setLog.needsSync = false
            }
            
            try storageService.saveContext()
        } catch {
            print("Failed to sync set logs: \(error.localizedDescription)")
        }
    }
    
    /// Syncs pending workouts to the remote API
    private func syncPendingWorkouts() async {
        do {
            let pendingWorkouts = try storageService.fetchPendingWorkouts()
            
            for workout in pendingWorkouts {
                // TODO: Implement API call to sync workout
                // For now, just mark as synced
                workout.needsSync = false
            }
            
            try storageService.saveContext()
        } catch {
            print("Failed to sync workouts: \(error.localizedDescription)")
        }
    }
    
    /// Forces a sync operation regardless of current state
    func forceSync() async {
        await attemptSync()
    }
}
